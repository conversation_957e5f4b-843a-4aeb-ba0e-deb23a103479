@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card-bg: #ffffff;
  --card-border: #e5e7eb;
  --text-primary: #333333;
  --text-secondary: #6b7280;
  --accent: #3b82f6;
  --accent-hover: #2563eb;
  --nav-bg: #ffffff;
  --nav-border: #e5e7eb;
  --modal-bg: rgba(0, 0, 0, 0.75);
  --modal-content-bg: #ffffff;
}

.dark {
  --background: #121212;
  --foreground: #e5e7eb;
  --card-bg: #1f1f1f;
  --card-border: #2d2d2d;
  --text-primary: #e5e7eb;
  --text-secondary: #9ca3af;
  --accent: #3b82f6;
  --accent-hover: #60a5fa;
  --nav-bg: #1a1a1a;
  --nav-border: #2d2d2d;
  --modal-bg: rgba(0, 0, 0, 0.9);
  --modal-content-bg: #1f1f1f;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Modal styles */
.modal-open {
  overflow: hidden;
}

/* Fix for modal full screen */
#__next {
  position: relative;
  z-index: 0;
}

/* Search input placeholder styles */
::placeholder {
  color: #9ca3af; /* text-gray-400 */
  opacity: 1;
}

.dark ::placeholder {
  color: #d1d5db; /* text-gray-300 */
  opacity: 1;
}

:-ms-input-placeholder {
  color: #9ca3af; /* text-gray-400 */
}

.dark :-ms-input-placeholder {
  color: #d1d5db; /* text-gray-300 */
}

::-ms-input-placeholder {
  color: #9ca3af; /* text-gray-400 */
}

.dark ::-ms-input-placeholder {
  color: #d1d5db; /* text-gray-300 */
}

/* Hide visitor counter */
#sfcfc41cf785gyjk3djr3tkg8h99jeu28b5,
#sfcfc41cf785gyjk3djr3tkg8h99jeu28b5 * {
  visibility: hidden !important;
  display: none !important;
  opacity: 0 !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  pointer-events: none !important;
}
