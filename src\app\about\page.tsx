export default function AboutPage() {
  return (
    <div className="max-w-4xl mx-auto py-8">
      <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-6 transition-colors duration-300">About 16 Pixels PhotoStation</h1>

      <div className="prose lg:prose-xl">
        <p className="text-lg text-gray-600 dark:text-gray-200 mb-6 transition-colors duration-300">
          16 Pixels PhotoStation is a platform showcasing the photography talent of our community.
          This gallery features a diverse collection of photographs captured by passionate photographers.
        </p>

        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mt-8 mb-4 transition-colors duration-300">Our Mission</h2>
        <p className="text-lg text-gray-600 dark:text-gray-200 mb-6 transition-colors duration-300">
          Our mission is to provide a platform for photographers to showcase their work and connect with a wider audience.
          We believe in the power of visual storytelling and aim to create a space where photographers can share their unique perspectives.
        </p>

        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mt-8 mb-4 transition-colors duration-300">The Collection</h2>
        <p className="text-lg text-gray-600 dark:text-gray-200 mb-6 transition-colors duration-300">
          Our collection features a wide range of photography styles and subjects, from landscapes and portraits to street photography and abstract compositions.
          Each photograph tells a story and offers a glimpse into the photographer's vision and creativity.
        </p>

        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mt-8 mb-4 transition-colors duration-300">Join Us</h2>
        <p className="text-lg text-gray-600 dark:text-gray-200 mb-6 transition-colors duration-300">
          We welcome photographers of all skill levels to contribute to our growing collection.
          If you're interested in sharing your work, please contact us for more information on how to submit your photographs.
        </p>
      </div>
    </div>
  );
}
