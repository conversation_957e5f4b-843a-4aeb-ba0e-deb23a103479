# 16 Pixels PhotoStation

A beautiful photo gallery portfolio website built with HTML, CSS, and JavaScript.

## Features

- Responsive photo gallery grid
- Hover over photos to see photographer information
- Styled with Tailwind CSS
- Automatically deployed to GitHub Pages

## Getting Started

Simply open the `index.html` file in your web browser to view the gallery.

## Adding Photos

1. Add your photos to the `photos` directory
2. Update the HTML file with the new photo information

## Deployment

The site is automatically deployed to GitHub Pages when changes are pushed to the main branch. The GitHub Actions workflow handles the deployment process.

## License

MIT
