<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>16 Pixels PhotoStation</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    .photo-card {
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }
    .photo-info {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      padding: 1rem;
      text-align: center;
    }
    .photo-card:hover .photo-info {
      opacity: 1;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <header class="bg-white shadow-sm">
    <div class="container mx-auto px-4 py-4 flex justify-between items-center">
      <h1 class="text-2xl font-bold text-gray-800">16 Pixels PhotoStation</h1>
      <div class="flex items-center space-x-4">
        <div class="relative">
          <input
            type="text"
            placeholder="Search photos..."
            class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button class="absolute right-3 top-1/2 transform -translate-y-1/2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </button>
        </div>
        <a href="#" class="text-blue-600 hover:text-blue-800">Login</a>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold text-gray-800 mb-4">Welcome to 16 Pixels PhotoStation</h2>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        Explore our collection of stunning photographs. Hover over any photo to see the photographer's details.
      </p>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      <!-- Photo 1 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo1.jpg" alt="Sunset at the Beach" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">Sunset at the Beach</h3>
          <p class="text-sm mb-2">by John Doe</p>
          <p class="text-xs">A beautiful sunset captured at the beach</p>
        </div>
      </div>

      <!-- Photo 2 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo2.jpg" alt="Mountain View" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">Mountain View</h3>
          <p class="text-sm mb-2">by Jane Smith</p>
          <p class="text-xs">Breathtaking view of mountains in the morning</p>
        </div>
      </div>

      <!-- Photo 3 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo3.jpg" alt="City Lights" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">City Lights</h3>
          <p class="text-sm mb-2">by Alex Johnson</p>
          <p class="text-xs">Night view of the city skyline</p>
        </div>
      </div>

      <!-- Photo 4 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo4.jpg" alt="Forest Path" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">Forest Path</h3>
          <p class="text-sm mb-2">by Sarah Williams</p>
          <p class="text-xs">A serene path through the dense forest</p>
        </div>
      </div>

      <!-- Photo 5 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo5.jpg" alt="Ocean Waves" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">Ocean Waves</h3>
          <p class="text-sm mb-2">by Michael Brown</p>
          <p class="text-xs">Powerful ocean waves crashing on rocks</p>
        </div>
      </div>

      <!-- Photo 6 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo6.jpg" alt="Desert Landscape" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">Desert Landscape</h3>
          <p class="text-sm mb-2">by Emily Davis</p>
          <p class="text-xs">Vast desert landscape at sunset</p>
        </div>
      </div>

      <!-- Photo 7 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo7.jpg" alt="Autumn Colors" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">Autumn Colors</h3>
          <p class="text-sm mb-2">by David Wilson</p>
          <p class="text-xs">Vibrant autumn colors in a forest</p>
        </div>
      </div>

      <!-- Photo 8 -->
      <div class="photo-card rounded-lg shadow-md overflow-hidden">
        <img src="photos/photo8.jpg" alt="Wildlife" class="w-full h-64 object-cover">
        <div class="photo-info">
          <h3 class="text-xl font-bold mb-1">Wildlife</h3>
          <p class="text-sm mb-2">by Lisa Anderson</p>
          <p class="text-xs">A rare capture of wildlife in natural habitat</p>
        </div>
      </div>
    </div>
  </main>

  <footer class="bg-white mt-12 py-8 border-t">
    <div class="container mx-auto px-4 text-center text-gray-600">
      <p>&copy; 2025 16 Pixels PhotoStation. All rights reserved.</p>
    </div>
  </footer>
</body>
</html>
